package cn.javafan.demo.http;

import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 连接池功能测试
 * 验证连接池的各种功能和配置
 */
public class ConnectionPoolTest {
    
    private PoolingHttpClientConnectionManager connectionManager;
    private CloseableHttpClient httpClient;
    
    @BeforeEach
    void setUp() {
        connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(10);
        connectionManager.setDefaultMaxPerRoute(5);
        
        httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .build();
    }
    
    @AfterEach
    void tearDown() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
        if (connectionManager != null) {
            connectionManager.shutdown();
        }
    }
    
    /**
     * 测试基本的HTTP请求功能
     */
    @Test
    void testBasicHttpRequest() throws IOException {
        HttpGet request = new HttpGet("http://httpbin.org/get");
        HttpResponse response = httpClient.execute(request);
        
        assertEquals(200, response.getStatusLine().getStatusCode());
        assertNotNull(response.getEntity());
        
        String content = EntityUtils.toString(response.getEntity());
        assertFalse(content.isEmpty());
        
        // 验证连接池状态
        PoolStats stats = connectionManager.getTotalStats();
        assertTrue(stats.getLeased() >= 0);
        assertTrue(stats.getAvailable() >= 0);
    }
    
    /**
     * 测试连接复用功能
     */
    @Test
    void testConnectionReuse() throws IOException {
        String url = "http://httpbin.org/get";
        
        // 发送第一个请求
        HttpGet request1 = new HttpGet(url);
        HttpResponse response1 = httpClient.execute(request1);
        EntityUtils.consume(response1.getEntity());
        
        PoolStats statsAfterFirst = connectionManager.getTotalStats();
        int connectionsAfterFirst = statsAfterFirst.getLeased() + statsAfterFirst.getAvailable();
        
        // 发送第二个请求
        HttpGet request2 = new HttpGet(url);
        HttpResponse response2 = httpClient.execute(request2);
        EntityUtils.consume(response2.getEntity());
        
        PoolStats statsAfterSecond = connectionManager.getTotalStats();
        int connectionsAfterSecond = statsAfterSecond.getLeased() + statsAfterSecond.getAvailable();
        
        // 验证连接被复用（总连接数不应该增加）
        assertEquals(connectionsAfterFirst, connectionsAfterSecond);
        assertEquals(200, response1.getStatusLine().getStatusCode());
        assertEquals(200, response2.getStatusLine().getStatusCode());
    }
    
    /**
     * 测试并发请求处理
     */
    @Test
    void testConcurrentRequests() throws InterruptedException {
        int threadCount = 8;
        int requestsPerThread = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount * requestsPerThread);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    try {
                        HttpGet request = new HttpGet("http://httpbin.org/get");
                        HttpResponse response = httpClient.execute(request);
                        EntityUtils.consume(response.getEntity());
                        
                        if (response.getStatusLine().getStatusCode() == 200) {
                            successCount.incrementAndGet();
                        } else {
                            failureCount.incrementAndGet();
                        }
                    } catch (IOException e) {
                        failureCount.incrementAndGet();
                    } finally {
                        latch.countDown();
                    }
                }
            });
        }
        
        assertTrue(latch.await(60, TimeUnit.SECONDS));
        executor.shutdown();
        
        int totalRequests = threadCount * requestsPerThread;
        assertEquals(totalRequests, successCount.get() + failureCount.get());
        assertTrue(successCount.get() > 0, "应该有成功的请求");
        
        // 验证连接池没有超出限制
        PoolStats stats = connectionManager.getTotalStats();
        assertTrue(stats.getLeased() + stats.getAvailable() <= connectionManager.getTotalStats().getMax());
    }
    
    /**
     * 测试连接池饱和情况
     */
    @Test
    void testConnectionPoolSaturation() throws InterruptedException, IOException {
        // 创建一个很小的连接池
        PoolingHttpClientConnectionManager smallPool = new PoolingHttpClientConnectionManager();
        smallPool.setMaxTotal(2);
        smallPool.setDefaultMaxPerRoute(1);
        
        RequestConfig config = RequestConfig.custom()
            .setConnectionRequestTimeout(1000) // 1秒超时
            .build();
        
        try (CloseableHttpClient client = HttpClients.custom()
                .setConnectionManager(smallPool)
                .setDefaultRequestConfig(config)
                .build()) {
            
            int threadCount = 5;
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            CountDownLatch latch = new CountDownLatch(threadCount);
            AtomicInteger timeoutCount = new AtomicInteger(0);
            
            for (int i = 0; i < threadCount; i++) {
                executor.submit(() -> {
                    try {
                        HttpGet request = new HttpGet("http://httpbin.org/delay/3");
                        HttpResponse response = client.execute(request);
                        EntityUtils.consume(response.getEntity());
                    } catch (ConnectionPoolTimeoutException e) {
                        timeoutCount.incrementAndGet();
                    } catch (IOException e) {
                        // 其他IO异常
                    } finally {
                        latch.countDown();
                    }
                });
            }
            
            assertTrue(latch.await(30, TimeUnit.SECONDS));
            executor.shutdown();
            
            // 应该有一些请求因为连接池饱和而超时
            assertTrue(timeoutCount.get() > 0, "应该有请求因连接池饱和而超时");
            
        } finally {
            smallPool.shutdown();
        }
    }
    
    /**
     * 测试连接池配置参数
     */
    @Test
    void testConnectionPoolConfiguration() {
        // 测试最大连接数配置
        assertEquals(10, connectionManager.getTotalStats().getMax());
        assertEquals(5, connectionManager.getDefaultMaxPerRoute());
        
        // 修改配置
        connectionManager.setMaxTotal(20);
        connectionManager.setDefaultMaxPerRoute(10);
        
        assertEquals(20, connectionManager.getTotalStats().getMax());
        assertEquals(10, connectionManager.getDefaultMaxPerRoute());
    }
    
    /**
     * 测试连接池清理功能
     */
    @Test
    void testConnectionPoolCleanup() throws IOException, InterruptedException {
        // 发送一些请求来创建连接
        for (int i = 0; i < 3; i++) {
            HttpGet request = new HttpGet("http://httpbin.org/get");
            HttpResponse response = httpClient.execute(request);
            EntityUtils.consume(response.getEntity());
        }
        
        PoolStats statsBeforeCleanup = connectionManager.getTotalStats();
        int connectionsBeforeCleanup = statsBeforeCleanup.getLeased() + statsBeforeCleanup.getAvailable();
        
        // 等待一段时间让连接变为空闲
        Thread.sleep(2000);
        
        // 清理空闲连接
        connectionManager.closeIdleConnections(1, TimeUnit.SECONDS);
        
        PoolStats statsAfterCleanup = connectionManager.getTotalStats();
        int connectionsAfterCleanup = statsAfterCleanup.getLeased() + statsAfterCleanup.getAvailable();
        
        // 验证连接数减少（空闲连接被清理）
        assertTrue(connectionsAfterCleanup <= connectionsBeforeCleanup);
    }
    
    /**
     * 性能基准测试
     */
    @Test
    void testPerformanceBenchmark() throws IOException {
        String url = "http://httpbin.org/get";
        int requestCount = 20;
        
        // 测试使用连接池的性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < requestCount; i++) {
            HttpGet request = new HttpGet(url);
            HttpResponse response = httpClient.execute(request);
            EntityUtils.consume(response.getEntity());
        }
        long timeWithPool = System.currentTimeMillis() - startTime;
        
        System.out.println("使用连接池完成 " + requestCount + " 个请求耗时: " + timeWithPool + "ms");
        System.out.println("平均每个请求耗时: " + (timeWithPool / requestCount) + "ms");
        
        // 验证性能合理（这里只是确保测试能完成，实际阈值需要根据环境调整）
        assertTrue(timeWithPool > 0);
        assertTrue(timeWithPool < 60000); // 不应该超过60秒
    }
}
