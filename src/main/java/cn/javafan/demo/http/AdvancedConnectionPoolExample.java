package cn.javafan.demo.http;

import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;

import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 高级连接池配置示例
 * 演示连接池的高级特性和优化技巧
 */
public class AdvancedConnectionPoolExample {
    
    public static void main(String[] args) throws IOException, InterruptedException {
        System.out.println("=== Apache HttpClient 连接池高级示例 ===");
        
        // 示例1：自定义连接池配置
        demonstrateCustomConfiguration();
        
        // 示例2：连接池监控和清理
        demonstratePoolMaintenanceAndMonitoring();
        
        // 示例3：多路由连接池配置
        demonstrateMultiRouteConfiguration();
    }
    
    /**
     * 示例1：自定义连接池配置
     */
    public static void demonstrateCustomConfiguration() throws IOException {
        System.out.println("\n--- 示例1：自定义连接池配置 ---");
        
        // 创建连接池管理器，设置连接存活时间
        PoolingHttpClientConnectionManager connectionManager = 
            new PoolingHttpClientConnectionManager(60, TimeUnit.SECONDS);
        
        // 基础配置
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(50);
        
        // 设置连接验证时间（空闲30秒后验证）
        connectionManager.setValidateAfterInactivity(30000);
        
        // 自定义Keep-Alive策略
        ConnectionKeepAliveStrategy keepAliveStrategy = (response, context) -> {
            // 检查服务器返回的Keep-Alive头
            return 30 * 1000; // 默认保持连接30秒
        };
        
        // 请求配置
        RequestConfig requestConfig = RequestConfig.custom()
            .setConnectTimeout(5000)           // 连接超时5秒
            .setSocketTimeout(30000)           // 读取超时30秒
            .setConnectionRequestTimeout(3000) // 从连接池获取连接超时3秒
            .build();
        
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setKeepAliveStrategy(keepAliveStrategy)
                .setDefaultRequestConfig(requestConfig)
                .build()) {
            
            // 发送测试请求
            HttpGet request = new HttpGet("http://httpbin.org/get");
            HttpResponse response = httpClient.execute(request);
            
            System.out.println("响应状态: " + response.getStatusLine());
            System.out.println("Keep-Alive策略已应用");
            
            EntityUtils.consume(response.getEntity());
            
            // 显示连接池配置信息
            System.out.println("连接池配置:");
            System.out.println("  最大总连接数: " + connectionManager.getTotalStats().getMax());
            System.out.println("  默认每路由最大连接数: " + connectionManager.getDefaultMaxPerRoute());
        }
    }
    
    /**
     * 示例2：连接池监控和清理
     */
    public static void demonstratePoolMaintenanceAndMonitoring() throws IOException, InterruptedException {
        System.out.println("\n--- 示例2：连接池监控和清理 ---");
        
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(20);
        connectionManager.setDefaultMaxPerRoute(10);
        
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build()) {
            
            // 启动连接池维护任务
            ScheduledExecutorService maintenanceExecutor = Executors.newScheduledThreadPool(1);
            
            // 定期清理过期和空闲连接
            maintenanceExecutor.scheduleAtFixedRate(() -> {
                try {
                    // 清理过期连接
                    connectionManager.closeExpiredConnections();
                    
                    // 清理空闲超过30秒的连接
                    connectionManager.closeIdleConnections(30, TimeUnit.SECONDS);
                    
                    // 打印连接池状态
                    PoolStats stats = connectionManager.getTotalStats();
                    System.out.println("连接池维护 - 可用:" + stats.getAvailable() + 
                        ", 活跃:" + stats.getLeased() + ", 总计:" + (stats.getAvailable() + stats.getLeased()));
                    
                } catch (Exception e) {
                    System.err.println("连接池维护失败: " + e.getMessage());
                }
            }, 10, 10, TimeUnit.SECONDS);
            
            // 发送一些请求来填充连接池
            for (int i = 0; i < 5; i++) {
                HttpGet request = new HttpGet("http://httpbin.org/get");
                HttpResponse response = httpClient.execute(request);
                EntityUtils.consume(response.getEntity());
                System.out.println("请求 " + (i + 1) + " 完成");
            }
            
            // 等待观察维护任务
            System.out.println("等待30秒观察连接池维护...");
            Thread.sleep(30000);
            
            maintenanceExecutor.shutdown();
        }
    }
    
    /**
     * 示例3：多路由连接池配置
     */
    public static void demonstrateMultiRouteConfiguration() throws IOException {
        System.out.println("\n--- 示例3：多路由连接池配置 ---");
        
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(10);
        
        // 为不同的服务配置不同的连接数
        HttpRoute httpbinRoute = new HttpRoute(new HttpHost("httpbin.org", 80, "http"));
        HttpRoute httpsRoute = new HttpRoute(new HttpHost("httpbin.org", 443, "https"));
        
        connectionManager.setMaxPerRoute(httpbinRoute, 20);  // HTTP路由20个连接
        connectionManager.setMaxPerRoute(httpsRoute, 30);    // HTTPS路由30个连接
        
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build()) {
            
            // 测试不同路由的请求
            testRoute(httpClient, "http://httpbin.org/get", "HTTP路由");
            testRoute(httpClient, "https://httpbin.org/get", "HTTPS路由");
            
            // 显示各路由的连接池状态
            System.out.println("\n路由连接池状态:");
            
            PoolStats httpStats = connectionManager.getStats(httpbinRoute);
            System.out.println("HTTP路由 - 可用:" + httpStats.getAvailable() + 
                ", 活跃:" + httpStats.getLeased() + ", 最大:" + httpStats.getMax());
            
            PoolStats httpsStats = connectionManager.getStats(httpsRoute);
            System.out.println("HTTPS路由 - 可用:" + httpsStats.getAvailable() + 
                ", 活跃:" + httpsStats.getLeased() + ", 最大:" + httpsStats.getMax());
            
            PoolStats totalStats = connectionManager.getTotalStats();
            System.out.println("总计 - 可用:" + totalStats.getAvailable() + 
                ", 活跃:" + totalStats.getLeased() + ", 最大:" + totalStats.getMax());
        }
    }
    
    private static void testRoute(CloseableHttpClient httpClient, String url, String routeName) throws IOException {
        System.out.println("\n测试 " + routeName + ":");
        
        for (int i = 0; i < 3; i++) {
            long startTime = System.currentTimeMillis();
            HttpGet request = new HttpGet(url);
            HttpResponse response = httpClient.execute(request);
            long responseTime = System.currentTimeMillis() - startTime;
            
            System.out.println("  请求 " + (i + 1) + " - 状态:" + response.getStatusLine().getStatusCode() + 
                ", 耗时:" + responseTime + "ms");
            
            EntityUtils.consume(response.getEntity());
        }
    }
}
