package cn.javafan.demo.http;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 基础连接池示例
 * 演示连接池的基本配置和使用方法
 */
public class BasicConnectionPoolExample {
    
    public static void main(String[] args) throws IOException, InterruptedException {
        System.out.println("=== Apache HttpClient 连接池基础示例 ===");
        
        // 示例1：基本连接池配置
        demonstrateBasicConfiguration();
        
        // 示例2：连接池状态监控
        demonstratePoolMonitoring();
        
        // 示例3：性能对比测试
        demonstratePerformanceComparison();
    }
    
    /**
     * 示例1：基本连接池配置
     */
    public static void demonstrateBasicConfiguration() throws IOException {
        System.out.println("\n--- 示例1：基本连接池配置 ---");
        
        // 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        
        // 设置最大连接数
        connectionManager.setMaxTotal(100);
        
        // 设置每个路由的最大连接数
        connectionManager.setDefaultMaxPerRoute(20);
        
        // 创建HttpClient
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build()) {
            
            // 发送请求
            HttpGet request = new HttpGet("http://httpbin.org/get");
            HttpResponse response = httpClient.execute(request);
            
            System.out.println("响应状态: " + response.getStatusLine());
            System.out.println("响应内容: " + EntityUtils.toString(response.getEntity()));
            
            // 打印连接池状态
            PoolStats stats = connectionManager.getTotalStats();
            System.out.println("连接池状态:");
            System.out.println("  总连接数: " + (stats.getLeased() + stats.getAvailable()));
            System.out.println("  活跃连接: " + stats.getLeased());
            System.out.println("  可用连接: " + stats.getAvailable());
        }
    }
    
    /**
     * 示例2：连接池状态监控
     */
    public static void demonstratePoolMonitoring() throws IOException, InterruptedException {
        System.out.println("\n--- 示例2：连接池状态监控 ---");
        
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(10);
        connectionManager.setDefaultMaxPerRoute(5);
        
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build()) {
            
            // 启动监控线程
            Thread monitorThread = new Thread(() -> {
                for (int i = 0; i < 10; i++) {
                    PoolStats stats = connectionManager.getTotalStats();
                    System.out.println("监控 " + (i + 1) + " - 可用:" + stats.getAvailable() + 
                        ", 活跃:" + stats.getLeased() + ", 等待:" + stats.getPending());
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
            monitorThread.start();
            
            // 并发发送请求
            ExecutorService executor = Executors.newFixedThreadPool(8);
            CountDownLatch latch = new CountDownLatch(15);
            
            for (int i = 0; i < 15; i++) {
                final int requestId = i + 1;
                executor.submit(() -> {
                    try {
                        HttpGet request = new HttpGet("http://httpbin.org/delay/2");
                        HttpResponse response = httpClient.execute(request);
                        EntityUtils.consume(response.getEntity());
                        System.out.println("请求 " + requestId + " 完成");
                    } catch (IOException e) {
                        System.err.println("请求 " + requestId + " 失败: " + e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            
            latch.await();
            executor.shutdown();
            monitorThread.join();
        }
    }
    
    /**
     * 示例3：性能对比测试
     */
    public static void demonstratePerformanceComparison() throws IOException, InterruptedException {
        System.out.println("\n--- 示例3：性能对比测试 ---");
        
        int requestCount = 50;
        String testUrl = "http://httpbin.org/get";
        
        // 测试不使用连接池
        long timeWithoutPool = testWithoutConnectionPool(testUrl, requestCount);
        System.out.println("不使用连接池耗时: " + timeWithoutPool + "ms");
        
        // 测试使用连接池
        long timeWithPool = testWithConnectionPool(testUrl, requestCount);
        System.out.println("使用连接池耗时: " + timeWithPool + "ms");
        
        // 计算性能提升
        double improvement = ((double)(timeWithoutPool - timeWithPool) / timeWithoutPool) * 100;
        System.out.println("性能提升: " + String.format("%.2f%%", improvement));
    }
    
    private static long testWithoutConnectionPool(String url, int requestCount) throws IOException {
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < requestCount; i++) {
            try (CloseableHttpClient client = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                HttpResponse response = client.execute(request);
                EntityUtils.consume(response.getEntity());
            }
        }
        
        return System.currentTimeMillis() - startTime;
    }
    
    private static long testWithConnectionPool(String url, int requestCount) throws IOException {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(20);
        connectionManager.setDefaultMaxPerRoute(10);
        
        try (CloseableHttpClient client = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build()) {
            
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < requestCount; i++) {
                HttpGet request = new HttpGet(url);
                HttpResponse response = client.execute(request);
                EntityUtils.consume(response.getEntity());
            }
            
            return System.currentTimeMillis() - startTime;
        }
    }
}
